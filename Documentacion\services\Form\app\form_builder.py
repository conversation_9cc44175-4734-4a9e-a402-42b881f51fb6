import streamlit as st
import sys
import os
# Add the root directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
from shared.db.database import SessionLocal, Base, engine
from shared.db.model import FormularioReporte

def save_formulario_json(json_data: dict):
    db = SessionLocal()

    try:
        nuevo_formulario = FormularioReporte(
            method=json_data.get("method"),
            operation=json_data.get("operation"),
            version=json_data.get("version"),
            autor=json_data.get("autor"),

            name_reporte=json_data.get("info_reporte", {}).get("name_reporte"),
            description_reporte=json_data.get("info_reporte", {}).get("description_reporte"),
            frequency_reporte=json_data.get("info_reporte", {}).get("frequency_reporte"),
            path_reporte=json_data.get("info_reporte", {}).get("path_reporte"),

            name_panel_reporte=json_data.get("info_tecnic_reporte", {}).get("name_Panel_reporte"),
            name_analisis_reporte=json_data.get("info_tecnic_reporte", {}).get("name_analisis_reporte"),
            num_tabs_reporte=json_data.get("info_tecnic_reporte", {}).get("num_tabs_reporte"),

            departments_report=json_data.get("info_tecnic_reporte", {}).get("department_report", []),
            cdatos_reporte=json_data.get("cdatos_reporte", []),
            filter_reporte=json_data.get("characteristics_reporte", {}).get("filter_reporte", []),
            calculated_reporte=json_data.get("characteristics_reporte", {}).get("calculated_reporte", [])
        )
        db.add(nuevo_formulario)
        db.commit()
        return True

    except Exception as e:
        db.rollback()
        print("❌ Error al guardar formulario en la base de datos:", e)
        return False

    finally:
        db.close()

def render_text(field, submitted_data, errores, key_suffix=""):
    label = field.get("label", field["name"])
    required = field.get("required", False)
    placeholder = field.get("placeholder", "")
    help_text = field.get("help", "")
    key = f"{field['name']}_{key_suffix}" if key_suffix else field["name"]
    valor = st.text_input(label, placeholder=placeholder, help=help_text, key=key)
    if required and not valor.strip():
        errores.append(f"El campo '{label}' es obligatorio.")
    submitted_data[field["name"]] = valor

def render_textarea(field, submitted_data, errores, key_suffix=""):
    label = field.get("label", field["name"])
    required = field.get("required", False)
    placeholder = field.get("placeholder", "")
    help_text = field.get("help", "")
    key = f"{field['name']}_{key_suffix}" if key_suffix else field["name"]
    valor = st.text_area(label, placeholder=placeholder, help=help_text, key=key)
    if required and not valor.strip():
        errores.append(f"El campo '{label}' es obligatorio.")
    submitted_data[field["name"]] = valor

def render_selectbox(field, submitted_data, errores, key_suffix=""):
    label = field.get("label", field["name"])
    options = field.get("options", [])
    required = field.get("required", False)
    help_text = field.get("help", "")
    key = f"{field['name']}_{key_suffix}" if key_suffix else field["name"]
    valor = st.selectbox(label, options, help=help_text, key=key)
    if required and not valor:
        errores.append(f"Debe seleccionar una opción en '{label}'.")
    submitted_data[field["name"]] = valor

def render_multiselect(field, submitted_data, errores, key_suffix=""):
    label = field.get("label", field["name"])
    options = field.get("options", [])
    required = field.get("required", False)
    help_text = field.get("help", "")
    new_option = field.get("new_opction", False)
    key = f"{field['name']}_{key_suffix}" if key_suffix else field["name"]
    valor = st.multiselect(label, options, help=help_text, key=key, accept_new_options=new_option)
    if required and not valor:
        errores.append(f"Debe seleccionar una opción en '{label}'.")
    submitted_data[field["name"]] = valor

def render_radio(field, submitted_data, errores, key_suffix=""):
    label = field.get("label", field["name"])
    options = field.get("options", [])
    required = field.get("required", False)
    help_text = field.get("help", "")
    key = f"{field['name']}_{key_suffix}" if key_suffix else field["name"]
    valor = st.radio(label, options, horizontal=True, help=help_text, key=key)
    if required and not valor:
        errores.append(f"Debe seleccionar una opción en '{label}'.")
    submitted_data[field["name"]] = valor

def render_slider(field, submitted_data, errores, key_suffix=""):
    label = field.get("label", field["name"])
    required = field.get("required", False)
    help_text = field.get("help", "")
    key = f"{field['name']}_{key_suffix}" if key_suffix else field["name"]
    min_value, max_value, value, step = field.get("value", [1,5,1,1])
    valor = st.slider(label, min_value,max_value, value, step, help=help_text, key=key)
    if required and not valor:
        errores.append(f"El campo '{label}' es obligatorio.")
    submitted_data[field["name"]] = valor

def render_segmented_control(field, submitted_data, errores, key_suffix=""):
    label = field.get("label", field["name"])
    options = field.get("options", [])
    required = field.get("required", False)
    help_text = field.get("help", "")
    key = f"{field['name']}_{key_suffix}" if key_suffix else field["name"]
    valor = st.segmented_control(label, options, key=key)
    if required and not valor:
        errores.append(f"Debe seleccionar una opción en '{label}'.")
    submitted_data[field["name"]] = valor

def render_columns(field, submitted_data, errores, key_suffix=""):
    label = field.get("label", field["name"])
    sub_name = field.get("name")
    sub_required = field.get("required", False)
    structure = field.get("structure", [])
    key_base = f"group_{sub_name}_{key_suffix}"

    if f"{key_base}_values" not in st.session_state:
        st.session_state[f"{key_base}_values"] = []

    if st.button(f"➕ Agregar {label}", key=f"{key_base}_add"):
        item = {etiqueta.lower().replace(" ", "_"): "" for etiqueta in structure}
        st.session_state[f"{key_base}_values"].append(item)

    new_values = []
    for idx, item in enumerate(st.session_state[f"{key_base}_values"]):
        cols = st.columns([1] * (len(item) + 1))
        values = {}
        for i, key in enumerate(item):
            label_i = structure[i] if i < len(structure) else key
            values[key] = cols[i].text_input(f"{label_i} {idx+1}", value=item[key], key=f"{key_base}_{key}_{idx}")
        remove = cols[-1].button("🗑️", key=f"{key_base}_remove_{idx}")
        if not remove:
            if sub_required and not all(v.strip() for v in values.values()):
                errores.append(f"Completa todos los campos en {label} #{idx+1}")
            new_values.append(values)

    st.session_state[f"{key_base}_values"] = new_values
    submitted_data[sub_name] = new_values

def render_group(field, submitted_data, errores, render_functions, key_suffix=""):
    st.subheader(field.get("label", field["name"]))
    group_name = field["name"]
    is_dynamic_group = field.get("delete_button", False)
    required = field.get("required", False)
    key_base = f"group_{group_name}_{key_suffix}"
    label = field.get("label", group_name)

    if is_dynamic_group:
        if f"{key_base}_entries" not in st.session_state:
            st.session_state[f"{key_base}_entries"] = [{}]

        if st.button(f"➕ Agregar {label}", key=f"{key_base}_add"):
            st.session_state[f"{key_base}_entries"].append({})

        new_entries = []
        for idx, entry in enumerate(st.session_state[f"{key_base}_entries"]):
            with st.expander(f"{label} #{idx+1}", expanded=True):
                temp_data = {}
                for subfield in field.get("elements", []):
                    sub_type = subfield.get("type")
                    render_func = render_functions.get(sub_type)
                    if render_func:
                        if sub_type == "columns":
                            render_func(subfield, temp_data, errores, key_suffix=f"{key_suffix}_{idx}")
                        elif sub_type == "group":
                            render_func(subfield, temp_data, errores, render_functions, key_suffix=f"{key_suffix}_{idx}")
                        else:
                            render_func(subfield, temp_data, errores, key_suffix=f"{key_suffix}_{idx}")
                remove = st.button("🗑️ Eliminar", key=f"{key_base}_remove_{idx}")
                if not remove:
                    new_entries.append(temp_data)

        st.session_state[f"{key_base}_entries"] = new_entries
        submitted_data[group_name] = new_entries

        if required and not new_entries:
            errores.append(f"Debe agregar al menos un ítem en '{label}'.")

    else:
        inner_data = {}
        for subfield in field.get("elements", []):
            sub_type = subfield.get("type")
            render_func = render_functions.get(sub_type)
            if render_func:
                if sub_type == "columns":
                    render_func(subfield, inner_data, errores, key_suffix=key_suffix)
                elif sub_type == "group":
                    render_func(subfield, inner_data, errores, render_functions, key_suffix=key_suffix)
                else:
                    render_func(subfield, inner_data, errores, key_suffix=key_suffix)
        submitted_data[group_name] = inner_data


def build_form(json_schema):
    st.header(json_schema.get("title", None))

    submitted_data = {}
    errores = []

    submitted_data["methods"] = "post"
    submitted_data["operation"] = f'New {json_schema.get("element_form")}'
    submitted_data["version"] = "1.0"
    submitted_data["autor"] = "xxxx"  # aqui debe ir el usuario que tiene login

    render_functions = {
        "text": render_text,
        "textarea": render_textarea,
        "selectbox": render_selectbox,
        "segmented_control": render_segmented_control,
        "columns": render_columns,
        "group": render_group,
        "slider":render_slider,
        "multiselect":render_multiselect
    }

    for idx, field in enumerate(json_schema.get("fields", [])):
        field_type = field.get("type")
        render_func = render_functions.get(field_type)

        if render_func:
            if field_type == "group":
                render_func(field, submitted_data, errores, render_functions, key_suffix=str(idx))
            else:
                render_func(field, submitted_data, errores, key_suffix=str(idx))
        else:
            st.warning(f"Tipo de campo no soportado: {field_type}")

    if st.button("Enviar"):
        if errores:
            st.error("❌ Por favor completa todos los campos obligatorios.")
            for error in errores:
                st.warning(error)
            st.json(submitted_data)
        else:  # esta es tu lógica
            if save_formulario_json(submitted_data):
                st.success("✅ Formulario enviado y guardado exitosamente.")
                return submitted_data
            else:
                st.error("❌ Ocurrió un error al guardar.")

    return None
