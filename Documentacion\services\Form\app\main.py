import streamlit as st
import json
import os
from form_builder import build_form
from utils.logger import setup_logger
from components.sidebar import render_sidebar

# Get user and session info
user = st.session_state.get("user", "invitado")
id_session = st.session_state.get("session_id", "xxxxxxxxx")

# Set up logger with user and session info
logger = setup_logger("form_service.main", user=user, session_id=id_session)

def main():
    logger.info(f"Application started by user {user}")
    
    # Render sidebar and get selected options
    sidebar_options = render_sidebar(user=user, session_id=id_session)
    
    # Only show forms if Formularios is selected
    if sidebar_options["selected_option"] == "Formularios":
        st.title("Formularios de documentación")
        
        # Use absolute path to find the form schemas
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        form_schemas_dir = os.path.join(base_dir, "form_schemas")
        
        formularios = {
            "Registro de Reporte": os.path.join(form_schemas_dir, "reportes.json")
        }

        tabs = st.tabs(list(formularios.keys()))
        logger.debug(f"Loading form schemas: {list(formularios.values())}")
        
        for i, (nombre, archivo) in enumerate(formularios.items()):
            with tabs[i]:
                try:
                    with open(archivo, encoding="utf-8") as f:
                        schema = json.load(f)
                    build_form(schema)
                except FileNotFoundError:
                    logger.error(f"Schema file not found: {archivo}")
                    st.error(f"No se pudo encontrar el esquema del formulario: {nombre}")
                    st.info(f"Ruta buscada: {archivo}")
    
    # Home option is handled by the redirect in the sidebar

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.exception(f"Unhandled exception for user {user}: {str(e)}")
        st.error("An unexpected error occurred. Please try again later.")
