2025-06-10 09:57:29 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 09:57:29 | ERROR    | form_service.main | invitado | xxxxxxxxx | main.py:43 | Unhandled exception for user invitado: [Errno 2] No such file or directory: '../form_schemas/reportes.json'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Form\app\main.py", line 41, in <module>
    main()
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Form\app\main.py", line 33, in main
    with open(archivo, encoding="utf-8") as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [<PERSON>rrno 2] No such file or directory: '../form_schemas/reportes.json'
2025-06-10 09:59:04 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:01:10 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:25:41 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:25:43 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:06 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:14 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:15 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:16 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:17 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:18 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:20 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:20 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:23 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:25 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:32 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:33 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:33 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:34 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:35 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:35 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:36 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:36 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:37 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
2025-06-10 10:43:37 | INFO     | form_service.main | invitado | xxxxxxxxx | main.py:15 | Application started by user invitado
