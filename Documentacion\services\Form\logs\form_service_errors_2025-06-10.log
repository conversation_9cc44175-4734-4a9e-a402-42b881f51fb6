2025-06-10 09:57:29 | ERROR    | form_service.main | invitado | xxxxxxxxx | main.py:43 | Unhandled exception for user invitado: [Errno 2] No such file or directory: '../form_schemas/reportes.json'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Form\app\main.py", line 41, in <module>
    main()
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Form\app\main.py", line 33, in main
    with open(archivo, encoding="utf-8") as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '../form_schemas/reportes.json'
