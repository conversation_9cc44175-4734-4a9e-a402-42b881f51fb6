import os
from pathlib import Path
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# Try to load .env file from shared directory
env_path = Path(__file__).resolve().parents[1] / '.env'
if env_path.exists():
    load_dotenv(dotenv_path=env_path)

# Get database configuration with fallback values for Docker environment
POSTGRES_USER = os.getenv("POSTGRES_USER", "form_user")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "una_contraseña_segura")
POSTGRES_DB = os.getenv("POSTGRES_DB", "formularios")
POSTGRES_HOST = os.getenv("POSTGRES_HOST", "postgres_db")  # Docker container name
POSTGRES_PORT = os.getenv("POSTGRES_PORT", "5432")

# Validate that we have all required values
if not all([POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB, POSTGRES_HOST, POSTGRES_PORT]):
    print("Database configuration:")
    print(f"  .env path: {env_path}")
    print(f"  .env exists: {env_path.exists()}")
    print(f"  User: {POSTGRES_USER}")
    print(f"  Password: {'*' * len(POSTGRES_PASSWORD) if POSTGRES_PASSWORD else 'None'}")
    print(f"  DB: {POSTGRES_DB}")
    print(f"  Host: {POSTGRES_HOST}")
    print(f"  Port: {POSTGRES_PORT}")
    raise ValueError("Missing required database environment variables")

DATABASE_URL = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

print(f"Connecting to database: postgresql://{POSTGRES_USER}:***@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}")

engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_size=5,
    max_overflow=10,
    echo=False  # Set to True for SQL debugging
)

SessionLocal = sessionmaker(bind=engine, autocommit=False, autoflush=False)

Base = declarative_base()

# Test database connection
def test_connection():
    """Test database connection and return status"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            # Verify we got a result
            row = result.fetchone()
            if row and row[0] == 1:
                return True, "Database connection successful"
            else:
                return False, "Database connection test query failed"
    except Exception as e:
        return False, f"Database connection failed: {str(e)}"