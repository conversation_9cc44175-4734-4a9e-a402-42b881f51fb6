-- Note: Database 'formularios' is already created by <PERSON><PERSON> Compose
-- This script runs in the context of that database

-- Create the form_user if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'form_user') THEN
        CREATE USER form_user WITH PASSWORD 'una_contraseña_segura';
    END IF;
END
$$;

-- Grant connection privileges to the current database
GRANT CONNECT ON DATABASE formularios TO form_user;
GRANT USAGE ON SCHEMA public TO form_user;

-- Grant table permissions (for existing and future tables)
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO form_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO form_user;

-- Set default privileges for future tables and sequences
ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT SELECT, INSERT, UPDATE ON TABLES TO form_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT USAGE, SELECT ON SEQUENCES TO form_user;

-- Ensure form_user can login (remove NOL<PERSON><PERSON><PERSON> if it was set)
ALTER ROLE form_user LOGIN;

-- Log the user creation
\echo 'Form user setup completed successfully'
