#!/usr/bin/env python3
"""
Database initialization script for the microservices system.
This script creates the necessary tables and verifies database connectivity.
"""

import sys
import os
from pathlib import Path

def init_db():
    """Initialize database tables and test connectivity"""
    try:
        from .database import engine, test_connection, Base
        from .model import FormularioReporte

        print("🔍 Testing database connection...")
        success, message = test_connection()

        if not success:
            print(f"❌ Database connection failed: {message}")
            return False

        print(f"✅ Database connection successful: {message}")

        print("🔧 Creating database tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully")

        # Verify table creation
        with engine.connect() as conn:
            from sqlalchemy import text
            result = conn.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
            """))
            tables = [row[0] for row in result]
            print(f"📋 Created tables: {', '.join(tables)}")

        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the correct environment with all dependencies installed.")
        return False

    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        return False

if __name__ == "__main__":
    success = init_db()
    sys.exit(0 if success else 1)