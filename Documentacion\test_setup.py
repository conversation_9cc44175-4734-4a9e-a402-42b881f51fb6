#!/usr/bin/env python3
"""
Test script to validate microservices setup and inter-container communication.
Run this script to verify that all components are working correctly.
"""

import sys
import os
import time
import subprocess
import requests
from pathlib import Path

def test_docker_compose():
    """Test if docker-compose.yml is valid"""
    print("🔍 Testing Docker Compose configuration...")
    try:
        result = subprocess.run(
            ["docker-compose", "config"], 
            cwd="Documentacion",
            capture_output=True, 
            text=True
        )
        if result.returncode == 0:
            print("✅ Docker Compose configuration is valid")
            return True
        else:
            print(f"❌ Docker Compose configuration error: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ Docker Compose not found. Please install Docker and Docker Compose.")
        return False
    except Exception as e:
        print(f"❌ Error testing Docker Compose: {e}")
        return False

def test_database_connection():
    """Test database connection from Python"""
    print("🔍 Testing database connection...")
    try:
        # Add shared directory to path
        sys.path.append(str(Path(__file__).parent / "Documentacion" / "shared"))

        from db.database import test_connection
        success, message = test_connection()

        if success:
            print(f"✅ Database connection successful: {message}")
            return True
        else:
            print(f"❌ Database connection failed: {message}")
            return False

    except ImportError as e:
        print(f"⚠️ Cannot test database connection (imports not available): {e}")
        print("This is normal if running outside Docker environment")
        return True  # Don't fail the test for import issues
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_form_user_login():
    """Test if form_user can login to database"""
    print("🔍 Testing form_user database login...")
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            database="formularios",
            user="form_user",
            password="una_contraseña_segura"
        )
        conn.close()
        print("✅ form_user can login successfully")
        return True
    except psycopg2.OperationalError as e:
        if "not permitted to log in" in str(e):
            print("❌ form_user login permission denied - run fix_database_user.sql")
        else:
            print(f"❌ form_user login failed: {e}")
        return False
    except ImportError:
        print("⚠️ psycopg2 not available - skipping form_user login test")
        return True
    except Exception as e:
        print(f"❌ Unexpected error testing form_user login: {e}")
        return False

def test_container_logs():
    """Check for critical errors in container logs"""
    print("🔍 Checking container logs for errors...")
    try:
        # Check for specific error patterns
        error_patterns = [
            ("home", [".venv/bin/activate: No such file", "grep: .env: No such file"]),
            ("formulario", ["not permitted to log in", "FATAL.*form_user"]),
            ("postgres_db", ["FATAL", "ERROR"])
        ]

        all_ok = True
        for container, patterns in error_patterns:
            try:
                result = subprocess.run(
                    ["docker-compose", "logs", container],
                    cwd="Documentacion",
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0:
                    logs = result.stdout.lower()
                    found_errors = []
                    for pattern in patterns:
                        if pattern.lower() in logs:
                            found_errors.append(pattern)

                    if found_errors:
                        print(f"❌ {container}: Found errors: {', '.join(found_errors)}")
                        all_ok = False
                    else:
                        print(f"✅ {container}: No critical errors found")
                else:
                    print(f"⚠️ Could not get logs for {container}")

            except subprocess.TimeoutExpired:
                print(f"⚠️ Timeout getting logs for {container}")
            except Exception as e:
                print(f"⚠️ Error checking {container} logs: {e}")

        return all_ok

    except Exception as e:
        print(f"❌ Error checking container logs: {e}")
        return False

def test_service_endpoints():
    """Test if services are responding"""
    print("🔍 Testing service endpoints...")
    
    services = {
        "Form Service": "http://localhost:8502",
        "Home Service": "http://localhost:8501",
        "Reverse Proxy": "http://localhost:80"
    }
    
    all_ok = True
    for service_name, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {service_name} is responding")
            else:
                print(f"⚠️ {service_name} returned status {response.status_code}")
                all_ok = False
        except requests.exceptions.ConnectionError:
            print(f"❌ {service_name} is not responding (connection refused)")
            all_ok = False
        except requests.exceptions.Timeout:
            print(f"❌ {service_name} timed out")
            all_ok = False
        except Exception as e:
            print(f"❌ Error testing {service_name}: {e}")
            all_ok = False
    
    return all_ok

def main():
    """Run all tests"""
    print("🚀 Starting microservices system validation...\n")

    tests = [
        ("Docker Compose Configuration", test_docker_compose),
        ("Container Log Analysis", test_container_logs),
        ("Database Connection", test_database_connection),
        ("Form User Login", test_form_user_login),
        ("Service Endpoints", test_service_endpoints)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Testing: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your microservices system is ready.")
        return True
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed. Please check the configuration.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
